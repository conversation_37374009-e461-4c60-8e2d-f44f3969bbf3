import asyncio
import logging
import time
from typing import List, Optional
import paho.mqtt.client as mqtt
from config.settings import load_config
from config.manage_api_client import ManageApiClient
import json

logger = logging.getLogger(__name__)

class MQTTClient:
    def __init__(self):
        self.client = mqtt.Client()
        self.broker_urls: List[str] = []
        self.username: str = ""
        self.password: str = ""
        self.client_id: str = ""
        self.is_connected: bool = False
        self.reconnect_interval: int = 5  # 重连间隔(秒)
        self.max_reconnect_attempts: int = 5  # 最大重连尝试次数
        
        # 设置回调函数
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        self.client.on_message = self._on_message
        
    def load_config(self):
        """从数据库加载MQTT配置"""
        try:
            config = load_config()
            
            # 直接从已加载的配置中获取MQTT参数
            mqtt_config = config.get("mqtt", {})
            
            broker_config = mqtt_config.get("broker")
            username_config = mqtt_config.get("username")
            password_config = mqtt_config.get("password")
            client_id_config = mqtt_config.get("client_id")
            
            self.broker_urls = broker_config.split(';') if broker_config else ["mqtt://broker.hivemq.com:1883"]
            self.username = username_config or ""
            self.password = password_config or ""
            self.client_id = client_id_config or f"xiaozhi_esp32_server_{int(time.time())}"
            
            if self.username and self.password:
                self.client.username_pw_set(self.username, self.password)
                
            if self.client_id:
                self.client.client_id = self.client_id
                
            logger.info(f"MQTT配置加载成功: brokers={self.broker_urls}")
            return True
        except Exception as e:
            logger.error(f"加载MQTT配置失败: {e}")
            return False
    
    def _get_param_from_api(self, param_code: str) -> str:
        """从API获取参数值"""
        try:
            # 调用API获取参数
            params_data = ManageApiClient._instance._execute_request(
                "GET", 
                f"/sys/params/getByKey?paramKey={param_code}"
            )
            if params_data and "paramValue" in params_data:
                return params_data["paramValue"]
            return ""
        except Exception as e:
            logger.warning(f"获取参数 {param_code} 失败: {e}")
            return ""
    
    def _on_connect(self, client, userdata, flags, rc):
        """连接回调函数"""
        if rc == 0:
            self.is_connected = True
            logger.info("MQTT连接成功")
            # 连接成功后可以订阅主题
            self._subscribe_topics()
        else:
            self.is_connected = False
            error_messages = {
                1: "协议版本不正确",
                2: "无效的客户端标识符",
                3: "服务器不可用",
                4: "用户名或密码错误",
                5: "未授权连接"
            }
            error_msg = error_messages.get(rc, f"未知错误代码: {rc}")
            logger.error(f"MQTT连接失败，错误代码: {rc} ({error_msg})")
    
    def _on_disconnect(self, client, userdata, rc):
        """断开连接回调函数"""
        was_connected = self.is_connected
        self.is_connected = False
        if was_connected:
            logger.info("MQTT连接已断开")
        if rc != 0:
            logger.warning(f"MQTT意外断开连接，错误代码: {rc}")
            # 启动重连机制
            asyncio.create_task(self._reconnect())
            
    def _on_message(self, client, userdata, msg):
        """消息接收回调函数"""
        logger.info(f"收到MQTT消息: topic={msg.topic}, payload={msg.payload.decode()}")
        # 处理接收到的消息
        self.handle_message(msg.topic, msg.payload.decode())
    
    def _subscribe_topics(self):
        """订阅主题"""
        # 这里可以定义需要订阅的主题
        topics = [
            ("xiaozhi/devices/+/status", 0),
            ("xiaozhi/devices/+/data", 0),
            ("xiaozhi/commands", 0),
            ("xiaozhi/ota/status", 0),
            ("xiaozhi/esp32", 0)
        ]
        
        for topic, qos in topics:
            self.client.subscribe(topic, qos)
            logger.info(f"订阅主题: {topic}")
    
    def handle_message(self, topic: str, payload: str):
        """处理接收到的MQTT消息"""
        # 根据主题和消息内容进行相应的处理
        # 这里可以根据你的业务需求来实现
        logger.info(f"处理MQTT消息: topic={topic}, payload={payload}")
        
        # 示例处理逻辑
        if topic.endswith("/status"):
            logger.info(f"设备状态更新: {payload}")
        elif topic.endswith("/data"):
            logger.info(f"设备数据上报: {payload}")
        elif topic == "xiaozhi/commands":
            logger.info(f"收到命令消息: {payload}")
        elif topic == "xiaozhi/ota/status":
            logger.info(f"OTA状态更新: {payload}")
        elif topic == "xiaozhi/esp32":
            logger.info(f"ESP32设备消息: {payload}")

            msg_json = json.loads(payload) if payload else {}
            
            # 构建响应消息
            response_msg = {}
            
            # 处理音频参数 (参考WebSocket实现)
            audio_params = msg_json.get("audio_params")
            if audio_params:
                format = audio_params.get("format")
                response_msg["audio_params"] = audio_params
            
            # 处理特性 (参考WebSocket实现)
            features = msg_json.get("features")
            if features:
                response_msg["features"] = features
                
                    
            # 发送响应消息
            response_topic = f"xiaozhi/esp32"
            self.publish(response_topic, json.dumps(response_msg, ensure_ascii=False))

        
        else:
            logger.info(f"收到其他主题消息: {topic} -> {payload}")
            

    
    async def _reconnect(self):
        """重连机制"""
        if self.is_connected:
            return
            
        logger.info("开始尝试重连MQTT Broker...")
        for attempt in range(1, self.max_reconnect_attempts + 1):
            logger.info(f"第 {attempt} 次尝试重连...")
            if self.connect():
                logger.info("MQTT重连成功")
                return
            else:
                logger.warning(f"第 {attempt} 次重连失败")
                if attempt < self.max_reconnect_attempts:
                    logger.info(f"等待 {self.reconnect_interval} 秒后再次尝试...")
                    await asyncio.sleep(self.reconnect_interval)
        
        logger.error(f"重连失败，已尝试 {self.max_reconnect_attempts} 次")
    
    def connect(self) -> bool:
        """连接到MQTT Broker"""
        if not self.broker_urls:
            logger.error("没有配置MQTT Broker地址")
            return False
            
        logger.info("正在尝试连接到MQTT Broker...")
        for broker_url in self.broker_urls:
            try:
                # 解析broker URL
                if broker_url.startswith("mqtt://"):
                    host_port = broker_url[7:]
                else:
                    host_port = broker_url
                    
                if ":" in host_port:
                    host, port = host_port.split(":")
                    port = int(port)
                else:
                    host = host_port
                    port = 1883
                    
                logger.info(f"尝试连接MQTT Broker: {host}:{port}")
                self.client.connect(host, port, 60)
                self.client.loop_start()
                # 等待连接结果
                time.sleep(0.1)
                return True
            except Exception as e:
                logger.warning(f"连接MQTT Broker {broker_url} 失败: {e}")
                continue
                
        logger.warning("无法连接到任何MQTT Broker")
        return False  # 连接失败返回False
    
    def disconnect(self):
        """断开MQTT连接"""
        self.client.loop_stop()
        self.client.disconnect()
        was_connected = self.is_connected
        self.is_connected = False
        if was_connected:
            logger.info("MQTT连接已断开")
    
    def publish(self, topic: str, payload: str, qos: int = 0, retain: bool = False):
        """发布消息"""
        if not self.is_connected:
            logger.debug("MQTT未连接，消息未发布")
            return False
            
        try:
            result = self.client.publish(topic, payload, qos, retain)
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.debug(f"消息发布成功: topic={topic}, payload={payload}")
                return True
            else:
                logger.error(f"消息发布失败: {result.rc}")
                return False
        except Exception as e:
            logger.error(f"发布消息时出错: {e}")
            return False

# 全局MQTT客户端实例
mqtt_client: Optional[MQTTClient] = None

async def init_mqtt():
    """初始化MQTT客户端"""
    global mqtt_client
    mqtt_client = MQTTClient()
    
    # 加载配置
    if not mqtt_client.load_config():
        logger.error("MQTT配置加载失败")
        return False
        
    # 连接到MQTT Broker
    if not mqtt_client.connect():
        logger.error("MQTT连接初始化失败")
        return False
        
    logger.info("MQTT客户端初始化完成")
    return True

def get_mqtt_client() -> Optional[MQTTClient]:
    """获取MQTT客户端实例"""
    global mqtt_client
    return mqtt_client

def publish_message(topic: str, payload: str, qos: int = 0, retain: bool = False):
    """发布消息到MQTT主题的便捷函数"""
    client = get_mqtt_client()
    if client:
        return client.publish(topic, payload, qos, retain)
    return False
