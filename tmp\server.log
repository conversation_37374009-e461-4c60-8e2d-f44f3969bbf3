2025-08-04 17:47:16 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 17:47:16 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 17:47:16 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 17:47:16 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 17:47:16 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 17:47:16 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 17:47:16 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 17:47:16 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 17:47:16 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 17:47:16 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 17:47:17 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 17:47:17 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 17:47:17 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 17:47:17 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 17:47:17 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 17:47:17 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 17:47:17 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 17:47:17 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 17:49:43 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 17:49:44 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 17:49:44 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 17:49:44 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 17:49:44 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 正在初始化MQTT客户端...
2025-08-04 17:49:44 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 17:49:44 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 17:49:44 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 17:49:44 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 17:49:44 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 17:49:44 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 17:51:49 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 17:51:50 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 17:51:50 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 17:51:50 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 17:51:50 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 正在初始化MQTT客户端...
2025-08-04 17:51:50 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 17:51:50 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 17:51:50 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 17:51:50 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 17:51:50 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 17:51:50 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 17:52:41 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 17:52:41 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 17:52:41 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 17:52:41 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 17:52:41 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 正在初始化MQTT客户端...
2025-08-04 17:52:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 17:52:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 17:52:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 17:52:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 17:52:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 17:52:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 17:54:41 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 17:54:41 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 17:54:41 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 17:54:41 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 17:54:41 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 正在初始化MQTT客户端...
2025-08-04 17:54:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 17:54:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 17:54:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 17:54:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 17:54:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 17:54:42 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 17:57:22 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 17:57:22 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 17:57:22 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 17:57:22 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 17:57:22 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 正在初始化MQTT客户端...
2025-08-04 17:57:22 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 17:57:22 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 17:57:22 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 17:57:22 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 17:57:22 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 17:57:22 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 17:59:30 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 17:59:30 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 17:59:30 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 17:59:30 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 17:59:30 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 正在初始化MQTT客户端...
2025-08-04 17:59:47 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 17:59:47 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 17:59:47 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 17:59:47 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 17:59:47 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 17:59:47 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 18:01:46 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:01:46 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:01:46 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:01:46 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:03:04 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:03:04 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:03:04 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:03:04 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:05:14 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:05:14 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:05:14 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:05:14 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:06:50 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:06:50 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:06:50 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:06:50 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:09:37 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:09:37 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:09:37 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:09:37 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:09:39 - 0.7.3_00000000000000 - __main__ - WARNING - __main__ - MQTT客户端初始化失败
2025-08-04 18:09:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 18:09:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 18:09:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 18:09:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 18:09:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 18:10:25 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:10:25 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:10:25 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:10:25 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:10:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 18:10:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 18:10:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 18:10:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 18:10:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 18:10:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 18:13:15 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:13:16 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:13:16 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:13:16 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:13:32 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 18:13:32 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 18:13:32 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 18:13:32 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 18:13:32 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 18:13:32 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 18:15:51 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:15:51 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:15:51 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:15:51 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:15:51 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 18:15:51 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 18:15:51 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 18:15:51 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 18:15:51 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 18:15:51 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-08-04 18:17:19 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-08-04 18:17:20 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 VAD_SileroVAD
2025-08-04 18:17:20 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-08-04 18:17:20 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 ASR_DoubaoStreamASR
2025-08-04 18:17:20 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - MQTT客户端初始化成功
2025-08-04 18:17:20 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://192.168.3.168:8003/mcp/vision/explain
2025-08-04 18:17:20 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://192.168.3.168:8000/xiaozhi/v1/
2025-08-04 18:17:20 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-08-04 18:17:20 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-08-04 18:17:20 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

